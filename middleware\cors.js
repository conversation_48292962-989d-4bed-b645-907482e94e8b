// cors.js
// CORS middleware

import cors from 'cors';

/**
 * CORS options
 */
const corsOptions = {
  origin: (origin, callback) => {
    // Allow requests with no origin (like mobile apps or curl)
    if (!origin) return callback(null, true);

    // List of allowed domains
    const allowedDomains = [
      "lovableproject.com",
      "lovable.app",
      "realizeanalytics.com",
      "https://palas-find-influencer-22.lovable.app",
      "https://palas.realizeanalytics.com",
      "https://preview--palas-find-influencer-22.lovable.app"
    ];

    // Check if the origin matches any of the allowed domains
    if (!origin) {
      return callback(null, true);
    }

    // Check for exact matches first
    if (allowedDomains.includes(origin)) {
      return callback(null, true);
    }

    // Check for domain endings (to handle subdomains and paths)
    for (const domain of allowedDomains) {
      // Skip the full URLs in our check for endings
      if (domain.startsWith('https://')) continue;

      if (origin.endsWith(domain)) {
        return callback(null, true);
      }
    }

    // Check if the origin is one of our full URLs but with a path
    for (const domain of allowedDomains) {
      if (domain.startsWith('https://') && origin.startsWith(domain)) {
        return callback(null, true);
      }
    }

    // If we get here, the origin is not allowed
    return callback(new Error("Not allowed by CORS"));
  },
  // Allow all HTTP methods
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
  // Allow credentials
  credentials: true,
  // Allow these headers
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
};

export { cors, corsOptions };
