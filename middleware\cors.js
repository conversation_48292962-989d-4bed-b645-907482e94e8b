// cors.js
import cors from 'cors';

const baseDomains = [
  'palas-find-influencer-22.lovable.app',
  'palas.realizeanalytics.com',
  'lovableproject.com',
  'lovable.app',
  'realizeanalytics.com'
];

const corsOptions = {
  origin: (origin, cb) => {
    // allow mobile / curl (no Origin header)
    if (!origin) return cb(null, true);

    try {
      const { hostname } = new URL(origin);

      // Is the request’s host exactly in the list …
      if (baseDomains.includes(hostname)) return cb(null, true);

      // … or is it a subdomain of something we trust?
      const ok = baseDomains.some(domain => hostname.endsWith(`.${domain}`));
      return ok ? cb(null, true) : cb(new Error('Not allowed by CORS'));
    } catch {
      return cb(new Error('Bad Origin header'));
    }
  },
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
  credentials: true,
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
};

export default cors(corsOptions);