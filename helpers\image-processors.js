// image-processors.js
// Image processing functions

import { joinImages } from 'join-images';
import crypto from 'crypto';
import https from 'https';
import { Buffer } from 'buffer';
import { getStorageInstance } from '../config/firebase-config.js';
import { DEFAULT_BUCKET_NAME } from '../config/constants.js';

/**
 * Process Instagram image posts
 * @param {Object} data - The Instagram data
 * @returns {string} - The base64-encoded merged image
 */
async function processInstagramImagePosts(data) {
  const imageUrls = [];

  // Check if Instagram data exists
  if (!data.instagram) {
    console.log("No Instagram data found.");
    return '';
  }

  // Append the HD profile picture as the first image, if available
  if (data.instagram.profile_picture_hd) {
    // Validate the profile picture URL
    if (typeof data.instagram.profile_picture_hd === 'string' && data.instagram.profile_picture_hd.trim() !== '') {
      imageUrls.push(data.instagram.profile_picture_hd);
      console.log(`Profile Picture Added: ${data.instagram.profile_picture_hd}`);
    } else {
      console.log('DEBUG: Profile picture URL is invalid or empty');
    }
  } else {
    console.log('DEBUG: No profile_picture_hd found in the data');
  }

  // Check if post_data exists and is an array
  if (Array.isArray(data.instagram.post_data) && data.instagram.post_data.length > 0) {
    console.log(`DEBUG: Total posts in data.instagram.post_data: ${data.instagram.post_data.length}`);
    console.log('DEBUG: Structure of first post:', JSON.stringify(data.instagram.post_data[0], null, 2));

    // Process each post: get the first image from each post's media array
    // DEBUG: Limit to the last 6 image posts as requested
    let processedCount = 0;
    data.instagram.post_data.forEach((post, index) => {
      console.log(`DEBUG: Examining post #${index + 1}`);

      // DEBUG: Check if post has media array
      if (!Array.isArray(post.media)) {
        console.log(`DEBUG: Post #${index + 1} has no media array or it's not an array`);
        console.log(`DEBUG: Post structure:`, JSON.stringify(post, null, 2));
        return; // Skip this post
      }

      console.log(`DEBUG: Post #${index + 1} has ${post.media.length} media items`);

      // DEBUG: Log the types of media in this post
      const mediaTypes = post.media.map(item => item.type);
      console.log(`DEBUG: Media types in post #${index + 1}:`, mediaTypes);

      const firstImageItem = post.media.find(mediaItem => mediaItem.type === 'image' && mediaItem.url);
      if (firstImageItem) {
        imageUrls.push(firstImageItem.url);
        processedCount++;
        console.log(`Image Found (${processedCount}): ${firstImageItem.url}`);
      } else {
        console.log(`DEBUG: No valid image found in post #${index + 1}`);
      }

      // DEBUG: Stop after processing 6 images (profile + 6 posts = 7 total)
      if (processedCount >= 6) {
        console.log(`DEBUG: Reached limit of 6 post images`);
        return;
      }
    });
  } else {
    console.log("Instagram post_data is not available or is not an array. Will use only profile picture if available.");
  }

  // DEBUG: Log final image count
  console.log(`DEBUG: Total images collected: ${imageUrls.length} (including profile pic)`);

  // If no images are found, return an empty string.
  if (imageUrls.length === 0) {
    console.log("No images found for analysis. Returning an empty string.");
    return '';
  }

  // Download all images concurrently with error handling
  console.log(`DEBUG: Attempting to download ${imageUrls.length} images`);

  try {
    // Use Promise.allSettled to handle individual download failures
    const downloadResults = await Promise.allSettled(imageUrls.map(url => downloadImage(url)));

    // Filter out failed downloads
    const successfulDownloads = downloadResults
      .filter(result => result.status === 'fulfilled')
      .map(result => result.value);

    console.log(`DEBUG: Successfully downloaded ${successfulDownloads.length} of ${imageUrls.length} images`);

    // If no images were successfully downloaded, return empty string
    if (successfulDownloads.length === 0) {
      console.log("No images could be downloaded successfully. Returning an empty string.");
      return '';
    }

    // Merge the images vertically using join-images
    const mergedImage = await joinImages(successfulDownloads, { direction: 'vertical' });
    console.log(`DEBUG: Images merged successfully`);

    // Convert the merged image to JPEG format and get the buffer
    const mergedBuffer = await mergedImage.toFormat('jpeg').toBuffer();
    const base64Image = mergedBuffer.toString('base64');
    console.log(`DEBUG: Final image converted to base64 (length: ${base64Image.length})`);

    return base64Image;
  } catch (error) {
    console.error(`Error processing images: ${error.message}`);
    return '';
  }
}

/**
 * Download an image from a URL
 * @param {string} url - The image URL
 * @returns {Buffer} - The image buffer
 */
function downloadImage(url) {
  return new Promise((resolve, reject) => {
    // Validate URL
    if (!url || typeof url !== 'string' || url.trim() === '') {
      return reject(new Error('Invalid image URL'));
    }

    // Set a timeout for the request (10 seconds)
    const timeout = 10000;
    const timeoutId = setTimeout(() => {
      req.destroy();
      reject(new Error(`Request timed out after ${timeout}ms`));
    }, timeout);

    const req = https.get(url, (res) => {
      // Clear the timeout
      clearTimeout(timeoutId);

      // Check for redirect
      if (res.statusCode >= 300 && res.statusCode < 400 && res.headers.location) {
        // Recursively follow redirects
        return downloadImage(res.headers.location)
          .then(resolve)
          .catch(reject);
      }

      // Check for successful status code
      if (res.statusCode !== 200) {
        return reject(new Error(`HTTP Error: ${res.statusCode}`));
      }

      // Check content type
      const contentType = res.headers['content-type'] || '';
      if (!contentType.startsWith('image/')) {
        return reject(new Error(`Invalid content type: ${contentType}`));
      }

      const chunks = [];
      res.on('data', (chunk) => chunks.push(chunk));
      res.on('end', () => {
        try {
          const buffer = Buffer.concat(chunks);
          // Ensure we have actual image data
          if (buffer.length === 0) {
            return reject(new Error('Empty image data received'));
          }
          resolve(buffer);
        } catch (error) {
          reject(error);
        }
      });
    });

    req.on('error', (error) => {
      clearTimeout(timeoutId);
      reject(error);
    });

    // Set a timeout for the entire request
    req.setTimeout(timeout, () => {
      req.destroy();
      clearTimeout(timeoutId);
      reject(new Error('Request timed out'));
    });
  });
}

/**
 * Cache an image in Cloud Storage
 * @param {string} imageUrl - The image URL
 * @param {string} influencerId - The influencer ID (optional)
 * @param {boolean} isProfilePicture - Whether this is a profile picture (optional)
 * @param {number} retryCount - Current retry attempt (internal use)
 * @returns {string} - The cached image URL
 */
async function cacheImage(imageUrl, influencerId = null, isProfilePicture = false, retryCount = 0) {
  if (!imageUrl) return "";

  const maxRetries = 3;
  const bucketName = DEFAULT_BUCKET_NAME;

  try {
    // Get storage instance
    const storage = getStorageInstance();

    // Generate a unique filename based on the URL
    const urlHash = crypto.createHash('md5').update(imageUrl).digest('hex');
    const filename = `${urlHash}.jpg`;

    // Determine the appropriate cache path based on parameters
    let cachePath;
    if (influencerId) {
      if (isProfilePicture) {
        cachePath = `influencers/${influencerId}/profile/${filename}`;
      } else {
        cachePath = `influencers/${influencerId}/images/${filename}`;
      }
    } else {
      cachePath = `image-cache/${filename}`;
    }

    // Check if the image is already cached
    try {
      const [metadata] = await storage.bucket(bucketName).file(cachePath).getMetadata();
      console.log(`Image already cached: ${imageUrl}`);

      // Check if the cached image has a signed URL that's still valid
      if (metadata.metadata && metadata.metadata.signedUrl) {
        const signedUrlExpiry = new Date(metadata.metadata.signedUrlExpiry || 0);
        if (signedUrlExpiry > new Date()) {
          console.log(`Using existing signed URL (valid until ${signedUrlExpiry.toISOString()})`);
          return metadata.metadata.signedUrl;
        }
      }

      return `https://storage.googleapis.com/${bucketName}/${cachePath}`;
    } catch (error) {
      // Image not cached, proceed with download
      console.log(`Image not cached, downloading: ${imageUrl}`);
    }

    // Download the image
    let contentType = 'image/jpeg';
    const buffer = await new Promise((resolve, reject) => {
      https.get(imageUrl, (res) => {
        contentType = res.headers['content-type'] || 'image/jpeg';
        const chunks = [];
        res.on('data', (chunk) => chunks.push(chunk));
        res.on('end', () => resolve(Buffer.concat(chunks)));
      }).on('error', reject);
    });

    // Calculate expiration date (30 days from now)
    const expirationDate = new Date();
    expirationDate.setDate(expirationDate.getDate() + 30);

    // Upload to Cloud Storage with metadata for expiration
    await storage.bucket(bucketName).file(cachePath).save(buffer, {
      metadata: {
        contentType: contentType,
        cacheControl: 'public, max-age=2592000', // 30 days in seconds
        metadata: {
          expiration: expirationDate.toISOString(),
          purgeAfter: '30days',
          cachedAt: new Date().toISOString()
        }
      }
    });

    // Set CORS configuration for the file
    await storage.bucket(bucketName).file(cachePath).setMetadata({
      metadata: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET',
        'Access-Control-Max-Age': '3600'
      }
    });

    // Make the file publicly accessible
    try {
      await storage.bucket(bucketName).file(cachePath).makePublic();
      console.log(`Image cached successfully: ${imageUrl}`);
      return `https://storage.googleapis.com/${bucketName}/${cachePath}`;
    } catch (permissionError) {
      console.warn(`Warning: Unable to make file public due to permissions. Using signed URL instead: ${permissionError.message}`);

      // Generate a signed URL as fallback (valid for 30 days)
      const [signedUrl] = await storage.bucket(bucketName).file(cachePath).getSignedUrl({
        version: 'v4',
        action: 'read',
        expires: Date.now() + 30 * 24 * 60 * 60 * 1000, // 30 days
      });

      // Store the signed URL and its expiry in the file's metadata
      await storage.bucket(bucketName).file(cachePath).setMetadata({
        metadata: {
          signedUrl: signedUrl,
          signedUrlExpiry: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString()
        }
      });

      console.log(`Generated signed URL for image: ${signedUrl}`);
      return signedUrl;
    }
  } catch (error) {
    console.error(`Error caching image (attempt ${retryCount + 1}/${maxRetries}): ${error.message}`);

    // Retry logic
    if (retryCount < maxRetries - 1) {
      console.log(`Retrying image caching in 1 second...`);
      await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1 second before retry
      return cacheImage(imageUrl, influencerId, isProfilePicture, retryCount + 1);
    }

    // After all retries failed, return the original URL
    console.error(`Failed to cache image after ${maxRetries} attempts: ${imageUrl}`);
    return imageUrl;
  }
}

export {
  processInstagramImagePosts,
  downloadImage,
  cacheImage
};
