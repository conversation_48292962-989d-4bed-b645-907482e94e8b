// analysis-service.js
// Analysis-related business logic

import { getFirestore } from 'firebase-admin/firestore';
import { WebAnalysis, AestheticAnalysis, ROIAnalysis, PartnershipAnalysis, FormatterAnalysis } from '../models/analysis.js';
import OpenAIConnector from '../connectors/openai-connector.js';
import { processInstagramImagePosts, downloadImage } from '../helpers/image-processors.js';
import { joinImages } from 'join-images';
import { extractJSON } from '../utils/string-utils.js';
import { writeJsonToBucket, getCachedJson, bucketName } from '../helpers/storage-helpers.js';
import { generateCombinedAnalysis, generateMergedAnalysis } from '../helpers/analysis-generators.js';
import { ROI_ANALYSIS_AGENT, PARTNERSHIP_ANALYSIS_AGENT, FORMATTER_AGENT, PHASE3_INSTRUCTIONS, PHASE5_INSTRUCTIONS, PARTNERSHIP_ANALYSIS_INSTRUCTIONS } from '../config/constants.js';
import { DEFAULT_CLIENT_ID } from '../config/constants.js';

/**
 * Retrieve campaign data from Firestore
 * @param {string} clientId - The client ID
 * @param {string} campaignId - The campaign ID
 * @returns {Object} - The campaign data
 */
async function getCampaignData(clientId = DEFAULT_CLIENT_ID, campaignId) {
  console.log(`[getCampaignData] Retrieving campaign data for ID: ${campaignId}, client: ${clientId}`);

  if (!campaignId) {
    console.error('[getCampaignData] Campaign ID is required but was not provided');
    throw new Error('Campaign ID is required to retrieve campaign data');
  }

  const db = getFirestore();
  console.log(`[getCampaignData] Executing Firestore query for campaign: ${campaignId}`);

  const startTime = performance.now();
  const campaignRef = db.collection('clients').doc(clientId).collection('campaigns').doc(campaignId);
  const campaignDoc = await campaignRef.get();
  const endTime = performance.now();

  console.log(`[getCampaignData] Firestore query took ${(endTime - startTime).toFixed(2)}ms`);

  if (!campaignDoc.exists) {
    console.error(`[getCampaignData] Campaign ${campaignId} not found in Firestore`);
    throw new Error(`Campaign not found: ${campaignId}`);
  }

  const campaignData = campaignDoc.data();

  // Validate campaign data structure
  if (!campaignData || !campaignData.name) {
    console.error(`[getCampaignData] Retrieved campaign data is invalid or incomplete`);
    throw new Error('Retrieved campaign data is invalid or incomplete');
  }

  console.log(`[getCampaignData] Successfully retrieved campaign: ${campaignData.name}`);
  console.log(`[getCampaignData] Campaign details:`, {
    id: campaignId,
    name: campaignData.name,
    product_description: campaignData.product_description?.substring(0, 50) + '...',
    influencer_niche: campaignData.influencer_niche,
    created_at: campaignData.created_at
  });

  return campaignData;
}

/**
 * Perform web analysis for an influencer
 * @param {string} clientId - The client ID
 * @param {string} campaignId - The campaign ID
 * @param {string} influencerId - The influencer ID
 * @param {string} influencerName - The influencer name
 * @param {string} influencerUsername - The influencer username
 * @returns {Object} - The web analysis
 */
async function performWebAnalysis(clientId = DEFAULT_CLIENT_ID, campaignId, influencerId, influencerName, influencerUsername) {
  const openai = new OpenAIConnector();
  const current_year = new Date().getFullYear();

  // Retrieve campaign data if campaignId is provided
  let campaignJSON = {};
  if (campaignId) {
    try {
      campaignJSON = await getCampaignData(clientId, campaignId);
      console.log(`Retrieved campaign data for campaign ${campaignId}`);
    } catch (error) {
      console.error(`Error retrieving campaign data: ${error.message}`);
      // Continue without campaign data if retrieval fails
    }
  }

  // Construct the prompt
  const phase3Prompt = `Project: Conduct Deep-Dive Background Research on Influencer **${influencerName}**

    Campaign your analysis is for: ${JSON.stringify(campaignJSON)}

    **Objective:** Perform a comprehensive web investigation on **${influencerName}** (also known as ${influencerUsername}) to gather historical data, reputation insights, and any red flags. This is a due diligence report combining public information from news, social media, forums, and other sources. The goal is to assess credibility, alignment, and risk factors.

    **Research Tasks & Methodology:**
    1. **Profile Confirmation & Aliases:** Verify the influencer’s identity and see if they have any alternate names, old handles, or common misspellings.
    - Search for variations of their name: '"${influencerName} real name"', '"${influencerName} alias"', and check if the influencer has been known by other handles in the past.
    - Also check if they have notable presence on other platforms under the same or different name (for example, search their Instagram handle on Twitter or YouTube).
    - *Output:* Note any alias or if the name is often confused with someone else (to avoid mix-ups).
    2. **Historical Timeline of Notable Events:** Compile a chronological timeline of key events in their career:
    - Look for news articles, press releases, or blog posts about ${influencerName}. Use time filters (e.g., Google Tools -> Time range) to find older references.
    - *Search queries:*
        - '"${influencerName}" interview <primary platform>'
        - '"${influencerName}" collaboration'
        - '"${influencerName}" ${current_year - 1}' (to find older news)
        - If they are a content creator, search for milestone events (e.g., hitting certain subscriber milestones, launching a product line, awards, etc.).
    - Note positive milestones (awards, major collaborations, viral content) and any negative incidents in order.
    - *Output:* A list of events with year/month and a brief description.
    3. **Press & Media Coverage:** Gather any significant media mentions:
    - Check news sites, magazines, or press releases. *Search queries:*
        - 'site:news.google.com "${influencerName}"'
        - 'site:prnewswire.com "${influencerName}"' (to catch press releases of brand collaborations)
        - '"${influencerName}" ${influencerName} article'
    - If the influencer has been interviewed or profiled, summarize the context.
    - If mentioned in any controversy in press, note the source and stance.
    - *Output:* A list of notable press mentions with source and date, or “None” if minimal.
    4. **Public Sentiment & Community Feedback:** Assess how the public (especially followers and general social media users) feel about ${influencerName}.
    - Search social forums and social media for discussions:
        - Reddit: Try '"${influencerName}" Reddit' or search relevant subreddits (like r/<niche> if exists, or r/YouTube, etc.) for threads about them.
        - Twitter/X: Search '"${influencerName}" controversy' or '"${influencerUsername}" -from:${influencerUsername}"' to see what others say (excluding their own tweets).
        - Look for patterns: Are people generally praising them or are there complaints?
    - If accessible, perform a quick sentiment analysis: for example, find a sample of comments/tweets and gauge how many are positive vs negative.
    - *Output:* Summarize the sentiment. Include a couple of direct quote snippets if relevant to illustrate tone.
    5. **Controversy & Risk History:** Investigate any past or present controversies, scandals, or issues:
    - *Search queries:*
        - '"${influencerName}" controversy'
        - '"${influencerName}" accused OR scandal OR backlash'
        - '"${influencerName}" apology' (often if they issued an apology publicly for something)
        - Also search specific incidents gleaned from earlier steps (e.g., if in  timeline found “backlash over XYZ”, dig deeper specifically on that).
        - Read between the lines and anticipate any negative reactions based on everything you found and know. Predicting backlash must be done without any lens of being nice, because we must protect the company from any potential controversies. Be clinical and ruthless in this prediction.
    - Check if any **brand safety issues**: remarks or content that conflict with our brand values (e.g., insensitive comments, involvement in political/extremist issues, etc.).
    - Rate the severity and recency: Is it a minor past issue or ongoing major problem?
    - *Output:* Describe each incident briefly with context and outcome. Also provide an overall risk level assessment like Low/Medium/High based on these (to feed into risk_level).
    6. **Authenticity & Fraud Flags:** Determine if there are any signs of inauthentic behavior:
    - Look for any evidence or accusations of fake follower use, engagement pods, or other fraudulent growth tactics.
    - *Search queries:*
        - '"${influencerName}" fake followers'
        - '"${influencerName} buying followers"'
        - Check if influencer marketing forums or websites have any analysis of them or the number of fake followers they have.
    - Also note if their engagement seems abnormally low or high relative to follower count (from Phase 2 data) – which could hint at fake followers or an extremely loyal audience respectively.
    - *Output:* Note any flags or a statement like “No significant fraud flags detected” if nothing comes up. If available.
    7. **Industry Reputation & Peer Perception:** Check if the influencer is respected in their content community or industry:
    - Search for any awards or recognitions.
    - See if other creators or industry figures mention them positively (or negatively). This might appear in blogs or YouTube commentary channels.
    - *Output:* List any accolades or notable positive recognition. If peers have criticized them, note that as well.
    8. **Compile Findings:** Organize the collected information into a structured format under relevant headings:
    - **aliases**: [list any other names/handles found]
    - **timeline_events**: [list key events in order]   MUST BE REAL AND NOT SIMULATED. If none are found return an empty object
    - **press_mentions**: [list of media coverage or interviews]  MUST BE REAL AND THE SOURCE PROVIDED. If no press mentions are found simply return an empty object.
    - **public_sentiment**: summary of audience sentiment (with any quantification if done and quotes if available)
    - **controversies**: [list of issues/controversies with brief description and outcome] MUST BE REAL AND THE SOURCE CITED.  If no controversies are found simply return an empty object.
    - **authenticity_flags**: note on follower/engagement authenticity (percentage of fake followers if known, etc.)
    - **industry_recognition**: [list of awards or positive peer recognition]   If no recognition are found simply return an empty object.
    - **overall_risk**: Overall risk assessment (Low/Medium/High) with rationale.

    **Output Requirements:**
    Provide the **deep-dive analysis as a single JSON object**
    {
  "name": "Full name of the influencer",
  "sentiment_score": 0-100 score representing overall sentiment,
  "risk_level": "Low", "Medium", or "High",
  "deep_dive_report": {
    "aliases": ["list", "of", "aliases"], //THIS MUST BE UTTERLY FACTUAL AND NOT SIMULATED
    "timeline_events": [
      {"year": "YYYY", "description": "Event description"}, //THIS MUST BE UTTERLY FACTUAL AND NOT SIMULATED
      ...
    ],
    "press_mentions": [
      {"source": "Source name", "date": "YYYY-MM-DD", "title": "Article title", "summary": "Brief summary", "sentiment": "positive/neutral/negative"}, //THIS MUST BE UTTERLY FACTUAL AND NOT SIMULATED
      ...
    ],
    "controversies": [
      {"title": "Controversy title", "description": "Description", "year": "YYYY", "severity": "Low/Medium/High", "resolution": "How it was resolved if applicable"}, //THIS MUST BE UTTERLY FACTUAL AND NOT SIMULATED
      ...
    ],
    "social_media_presence": {
      "platforms": ["Instagram", "Twitter", etc.],
      "tone": "Description of overall tone",
      "engagement_quality": "Description of engagement quality" //Provide an educated estimate when necessary for THIS SPECIFIC FIELD
    },
    "brand_associations": ["<Normalized Brand Name>-<Conflict or Non-Conflict>", "<Normalized Brand Name>-<Conflict or Non-Conflict>", ...], //HIGHLIGHT COMPETITORS OR POTENTIAL CONFLICTS
    "audience_demographics": {
      "age_range": "e.g. 18-34", //Provide an educated estimate when necessary for THIS SPECIFIC FIELD
      "gender_split": "e.g. 60% female, 40% male", //Provide an educated estimate when necessary for THIS SPECIFIC FIELD
      "interests": ["Interest1", "Interest2", ...]
    }
  }
}

    Under NO circumstances should you invent or 'simulate' any results!!! Some influencers just won't have information like this. It is better to find nothing than to invent something.

    Ensure the JSON is well-structured and **comprehensive**. It should contain all the above sections filled with the findings for **${influencerName}**, and nothing outside the JSON format.
    UNDER NO CIRCUMSTANCES DOES OLIVIA RETURN FAKE INFORMATION OR SIMULATE RESULTS AS SHE KNOWS IT WILL DESTROY THE REPUTATION SHE HAS SPENT DECADES BUILDING!

    The MANDATORY JSON Structure is below but ENSURE THAT YOU CREATE AN OBJECT OF ANALYSES USING THIS STRUCTURE AND DO NOT SIMPLY RETURN THE STRUCTURE ITSELF!!!!:

    {
  "name": "Full name of the influencer",
  "sentiment_score": 0-100 score representing overall sentiment,
  "risk_level": "Low", "Medium", or "High",
  "deep_dive_report": {
    "aliases": ["list", "of", "aliases"],
    "timeline_events": [
      {"year": "YYYY", "description": "Event description"},
      ...
    ],
    "press_mentions": [
      {"source": "Source name", "date": "YYYY-MM-DD", "title": "Article title", "summary": "Brief summary", "sentiment": "positive/neutral/negative"},
      ...
    ],
    "controversies": [
      {"title": "Controversy title", "description": "Description", "year": "YYYY", "severity": "Low/Medium/High", "resolution": "How it was resolved if applicable"},
      ...
    ],
    "social_media_presence": {
      "platforms": ["Instagram", "Twitter", etc.],
      "tone": "Description of overall tone",
      "engagement_quality": "Description of engagement quality"
    },
    "brand_associations": ["<Normalized Brand Name>-<Conflict or Non-Conflict>", "<Normalized Brand Name>-<Conflict or Non-Conflict>", ...], //HIGHLIGHT COMPETITORS OR POTENTIAL CONFLICTS
    "audience_demographics": {
      "age_range": "e.g. 18-34",
      "gender_split": "e.g. 60% female, 40% male",
      "interests": ["Interest1", "Interest2", ...]
    }
  }
}`;

  // Process the prompt with OpenAI using the Responses API with web search
  console.log("Using Responses API with web search for web analysis...");
  console.log("Web analysis prompt:", phase3Prompt);

  try {
    // Use Responses API with web search but without JSON mode
    console.log("Using Responses API with web search (without JSON mode)...");

    // Add explicit instructions to format the response as JSON
    const enhancedInstructions = `${PHASE3_INSTRUCTIONS}\n\nIMPORTANT: Format your response as a valid JSON object with the following structure:
{
  "name": "Full name of the influencer",
  "sentiment_score": 0-100 score representing overall sentiment,
  "risk_level": "Low", "Medium", or "High",
  "deep_dive_report": {
    "aliases": ["list", "of", "aliases"],
    "timeline_events": [
      {"year": "YYYY", "description": "Event description"},
      ...
    ],
    "press_mentions": [
      {"source": "Source name", "date": "YYYY-MM-DD", "title": "Article title", "summary": "Brief summary", "sentiment": "positive/neutral/negative"},
      ...
    ],
    "controversies": [
      {"title": "Controversy title", "description": "Description", "year": "YYYY", "severity": "Low/Medium/High", "resolution": "How it was resolved if applicable"},
      ...
    ],
    "social_media_presence": {
      "platforms": ["Instagram", "Twitter", etc.],
      "tone": "Description of overall tone",
      "engagement_quality": "Description of engagement quality"
    },
    "brand_associations": ["<Normalized Brand Name>-<Conflict or Non-Conflict>", "<Normalized Brand Name>-<Conflict or Non-Conflict>", ...], //HIGHLIGHT COMPETITORS OR POTENTIAL CONFLICTS
    "audience_demographics": {
      "age_range": "e.g. 18-34",
      "gender_split": "e.g. 60% female, 40% male",
      "interests": ["Interest1", "Interest2", ...]
    }
  }
}`;

    const response = await openai.client.responses.create({
      model: "gpt-4.1",
      instructions: enhancedInstructions,
      tools: [{ type: "web_search" }],
      input: [
        {
          role: "user",
          content: [
            { type: "input_text", text: phase3Prompt }
          ]
        }
      ]
    });

    // Log the full response for debugging
    console.log("Full web analysis response:", JSON.stringify(response, null, 2));

    // Extract JSON from the response
    let deepDiveResultsJSON;
    let responseText = '';

    if (response.output_text) {
      responseText = response.output_text;
    } else if (response.output && response.output.length > 0) {
      const messageOutput = response.output.find(item => item.type === "message");
      if (messageOutput && messageOutput.content && messageOutput.content.length > 0) {
        const textContent = messageOutput.content.find(content => content.type === "output_text");
        if (textContent && textContent.text) {
          responseText = textContent.text;
        }
      }
    }

    if (responseText) {
      console.log("Extracting JSON from response text...");
      deepDiveResultsJSON = extractJSON(responseText);

      if (!deepDiveResultsJSON) {
        console.log("Failed to extract JSON, attempting to parse the entire response...");
        try {
          // Try to parse the entire response as JSON
          deepDiveResultsJSON = JSON.parse(responseText.trim());
        } catch (parseError) {
          console.error("Failed to parse response as JSON:", parseError);

          // Create a minimal structure with the raw text
          deepDiveResultsJSON = {
            name: influencerName,
            sentiment_score: 50,
            risk_level: "Medium",
            deep_dive_report: {
              raw_analysis: responseText,
              aliases: [],
              timeline_events: [],
              press_mentions: [],
              controversies: []
            }
          };
        }
      }
    } else {
      console.error("No text content found in the response");
      // Create a minimal structure
      deepDiveResultsJSON = {
        name: influencerName,
        sentiment_score: 50,
        risk_level: "Medium",
        deep_dive_report: {
          aliases: [],
          timeline_events: [],
          press_mentions: [],
          controversies: []
        }
      };
    }

    // Save the raw response for debugging
    const outputPath = `test_outputs/${influencerUsername}_web_analysis_raw_${Date.now()}.json`;
    const fs = await import('fs');
    fs.writeFileSync(outputPath, JSON.stringify(response, null, 2));
    console.log(`Raw response saved to ${outputPath}`);

    // Store the web analysis in Firestore
    try {
      const webAnalysisId = await WebAnalysis.create(influencerId, deepDiveResultsJSON, clientId, campaignId);
      console.log(`Web analysis stored in Firestore with ID: ${webAnalysisId}`);
    } catch (firestoreError) {
      console.error("Error storing web analysis in Firestore:", firestoreError);
    }

    return deepDiveResultsJSON;
  } catch (error) {
    console.error('Error using Responses API with web search:', error);

    // Create a minimal structure for testing
    console.log("Creating minimal web analysis data for testing");
    const deepDiveResultsJSON = {
      name: influencerName,
      sentiment_score: 50,
      risk_level: "Medium",
      deep_dive_report: {
        aliases: [],
        timeline_events: [
          { year: new Date().getFullYear().toString(), description: "No timeline events found due to API error" }
        ],
        press_mentions: [],
        controversies: [],
        social_media_presence: {
          platforms: ["Instagram"],
          tone: "Unknown due to API error",
          engagement_quality: "Unknown due to API error"
        },
        brand_associations: [],
        audience_demographics: {
          age_range: "Unknown",
          gender_split: "Unknown",
          interests: []
        }
      }
    };

    // Store the web analysis in Firestore
    try {
      const webAnalysisId = await WebAnalysis.create(influencerId, deepDiveResultsJSON, clientId, campaignId);
      console.log(`Minimal web analysis stored in Firestore with ID: ${webAnalysisId}`);
    } catch (firestoreError) {
      console.error("Error storing minimal web analysis in Firestore:", firestoreError);
    }

    return deepDiveResultsJSON;
  }
}

/**
 * Perform aesthetic analysis for an influencer
 * @param {string} clientId - The client ID
 * @param {string} campaignId - The campaign ID
 * @param {string} influencerId - The influencer ID
 * @param {string} influencerName - The influencer name
 * @param {Object} influencerData - The influencer data
 * @param {Object} webAnalysisData - The web analysis data
 * @returns {Object} - The aesthetic analysis
 */
async function performAestheticAnalysis(clientId = DEFAULT_CLIENT_ID, campaignId, influencerId, influencerName, influencerData, webAnalysisData) {
  const openai = new OpenAIConnector();

  // Get the campaign data using the helper function
  let campaignJSON = {};
  try {
    campaignJSON = await getCampaignData(clientId, campaignId);
    console.log(`Retrieved campaign data for campaign ${campaignId} for aesthetic analysis`);
  } catch (error) {
    console.error(`Error retrieving campaign data for aesthetic analysis: ${error.message}`);
    throw error; // Aesthetic analysis requires campaign data
  }

  // Process Instagram image posts
  let influencerImagesBase64;
  let profileImageOnly = false;
  let noImagesAvailable = false;

  try {
    influencerImagesBase64 = await processInstagramImagePosts(influencerData);

    // Check if we only have a profile image (no post data)
    if (influencerData.instagram &&
        influencerData.instagram.profile_picture_hd &&
        (!Array.isArray(influencerData.instagram.post_data) || influencerData.instagram.post_data.length === 0)) {
      console.log('Only profile image is available for aesthetic analysis');
      profileImageOnly = true;
    }
  } catch (error) {
    console.error(`Error processing Instagram images: ${error.message}`);

    // If there's an error but we have a profile picture, try to use just that
    if (influencerData.instagram && influencerData.instagram.profile_picture_hd) {
      console.log('Attempting to use only profile picture for aesthetic analysis');
      try {
        const profileUrl = influencerData.instagram.profile_picture_hd;
        const imageBuffer = await downloadImage(profileUrl);
        const mergedImage = await joinImages([imageBuffer], { direction: 'vertical' });
        const mergedBuffer = await mergedImage.toFormat('jpeg').toBuffer();
        influencerImagesBase64 = mergedBuffer.toString('base64');
        profileImageOnly = true;
      } catch (profileError) {
        console.error(`Failed to process profile image: ${profileError.message}`);
        noImagesAvailable = true;
      }
    } else {
      console.log('No profile image or post images available for aesthetic analysis');
      noImagesAvailable = true;
    }
  }

  if (!influencerImagesBase64 && !noImagesAvailable) {
    throw new Error('No images found for aesthetic analysis');
  }

  // If no images are available, return a default aesthetic analysis
  if (noImagesAvailable) {
    console.log('Creating default aesthetic analysis due to lack of images');
    const defaultAestheticAnalysis = {
      name: influencerName,
      brand_fit_score: 50, // Neutral score
      content_analysis: {
        visual_fit: 50,
        tone_fit: "Unable to determine tone fit due to lack of visual content",
        content_themes: ["No visual content available for analysis"],
        image_analyses: [],
        red_flags: ["No visual content available for analysis"],
        notable_strengths: ["Unable to determine strengths due to lack of visual content"],
        notable_weaknesses: ["Unable to determine weaknesses due to lack of visual content"],
        sentiment_summary: "No visual content was available for analysis. This assessment is based solely on available text data and should be considered incomplete. A full aesthetic analysis requires visual content."
      }
    };

    // Store the default aesthetic analysis in Firestore
    const aestheticAnalysisId = await AestheticAnalysis.create(clientId, campaignId, influencerId, defaultAestheticAnalysis);
    console.log(`Default aesthetic analysis stored in Firestore with ID: ${aestheticAnalysisId}`);

    return defaultAestheticAnalysis;
  }

  // Construct the prompt
  const phase5Prompt = `Project: Qualitative Content and Aesthetic Analysis for **${influencerName}**

    **Objective:** Evaluate **${influencerName}**’s recent content (visuals, videos, captions, overall style) to determine how well it aligns with our brand’s aesthetic and messaging. Identify strengths in content style, any mismatches or red flags, and summarize the influencer’s tone and visual themes. This complements the quantitative data with a creative fit assessment.

    **Inputs:**
    - Campaign Analysis: [${JSON.stringify(campaignJSON)}]
    - Base64 Encoded Image which should be the primary purpose of your analysis.
    ${profileImageOnly ? '- NOTE: Only the profile image is available for this analysis. Focus your analysis on the profile image and any information available in the influencer\'s bio and username.' : ''}

    **Analysis Tasks:**
    1. **Visual Style Assessment:**
        - Examine the provided images (provided as a base64 encoded image) for common elements: color palette, lighting, settings, composition, physical fit for the brand, subject matter, message, and anything else relevant. Sample considerations are below but are not limited to these... you will be evaluating many types of campaigns and influencers and the analysis should be detailed and clinical for the specific campaign.
        ${profileImageOnly ? '- NOTE: Since only the profile image is available, focus on analyzing the profile picture in detail. Consider the composition, color scheme, subject matter, and overall aesthetic of the profile image. Make reasonable inferences about the influencer\'s style based on this single image.' : ''}
        - Are the colors mostly bright and vibrant or muted and neutral? (the campaign aesthetic might call for one or the other.)
        - What environments or imagery recur? (e.g., lots of nature shots, indoor home settings, product close-ups, people/action shots?)
        - Does the influencer have a consistent editing style or filter? (cohesive look vs. varied).
        - Compare with brand aesthetic requirements:
        - If brand is about **vibrant youthfulness** and the influencer’s images are indeed colorful and energetic, note that alignment (“High visual alignment: content is colorful and dynamic, matching brand vibe”).
        - If there’s a mismatch (brand is minimalist but influencer uses very busy/flashy visuals), note the discrepancy.
        - Rate the visual alignment on a simple scale (e.g., 1-5 or percentage) and provide reasoning.
    2. **Content Themes & Subjects:**
        - What topics or activities are depicted in content? (Workouts, family life, product reviews, travel, etc.)
        - Do these align with our campaign’s content themes? Highlight overlaps (e.g., influencer often posts about empowerment or sustainability, which are our themes).
        - Note if any significant part of their content is outside our interest (e.g., they also frequently post about a hobby or topic irrelevant or potentially conflicting with our brand).
        - Check if they have sponsored content: how do they integrate ads? (If possible to tell – e.g., "#ad" posts – do they still feel authentic?)
    3. **Caption and Communication Tone:**
        - Read through several captions or listen to how they speak in videos:
        - Is the tone casual and friendly, or formal and informative? Do they use humor or emotive storytelling?
        - Count usage of emojis, slang, or exclamation points as clues to tone (lots of "😂" means humorous, etc.).
        - Are captions typically long and narrative, or short and to the point?
        - Compare with brand’s desired tone the influencer personality/brand voice:
        - If brand voice is playful and the influencer writes playful captions with personal anecdotes, that’s a good match.
        - If brand is sophisticated and the influencer uses a lot of internet slang or coarse language, mention that as a potential mismatch.
        - Mention any notable patterns: e.g., “Frequently uses motivational quotes,” “Engages audience with questions in captions,” or “Sometimes snarky in replies to comments.”
    4. **Engagement and Community Interaction (Qualitative):**
        - Look at the nature of comments on their posts (we might not have all comments text, but gauge from sentiment):
        - Are people positive and inspired (“You’re so inspiring!”), or are there arguments/trolls visible?
        - Does the influencer respond to comments? (Engagement style: interactive vs. aloof.)
        - This reflects on how they handle community – important for brand partnerships (a highly engaged, positive community is a plus).
        - If sentiment analysis from Phase 3 indicated certain themes (like followers love their authenticity), see if the content examples illustrate why.
    5. **Red Flag Content Scan (Deep Dive of Content):**
        - Beyond known controversies, scan content for anything problematic:
        - Political or polarizing statements, offensive language or imagery, excessive profanity.
        - Endorsement of competitor brands (e.g., have they promoted a direct competitor’s product recently?).
        - Inconsistencies: any recent shift in content that is concerning (like suddenly very little posting, or a drastic change of style, which might indicate an issue).
        - Note any instance: e.g., “One post from June contains a joke that could be seen as insensitive -> flagging for awareness.”
        - If nothing major, state that content appears brand-safe at surface level.
    6. **Strengths & Weaknesses (Content Perspective):**
        - Summarize what this influencer excels at content-wise: e.g., “High production quality in videos,” “Very relatable storytelling,” “Great eye for design in photos.”
        - Also note any weaknesses or uncertainties: e.g., “Caption grammar isn’t the best (lots of typos),” or “Visual style is great but seems to vary widely, might lack consistency.”
    7. **Output – Qualitative Analysis Structured Data:**
        - Structure the findings into the 'content_analysis' JSON field with keys:
        - 'visual_fit' – a qualitative score or rating of visual alignment (and possibly a brief descriptor, e.g., "High", "Medium", or numeric 0-100).
        - 'tone_fit' – description of the influencer’s tone and how it fits (e.g., "Casual, friendly tone – fits well with our playful brand voice").
        - 'content_themes' – list of prevalent content themes/topics the influencer posts about (could cross-reference with brand themes).
        - 'red_flags' – list of any content-related red flags found (if none, can be an empty list or note "None").
        - 'notable_strengths' – list of positive observations in content (a quick bullet-like summary).
        - 'notable_weaknesses' – list of any concerns or downsides observed in content.
        - 'sentiment_summary' – a short recap of audience sentiment in context of their content, e.g., "Comments on posts are 90% positive, indicating a supportive community."
        - Also update 'influencers.brand_fit_score' if appropriate: This score synthesizes how well the influencer matches the brand overall. We can derive it from visual_fit and tone_fit (and possibly audience match). For example, if visual and tone are both high, brand_fit_score might be a high number. Include it as an integer 1-100 or similar.
        - Ensure this JSON integrates with previous fields (we will combine it with data from other phases in the final report, so use the same 'table.field' style).
        - Remember: this output is used for internal evaluation and final report crafting, so be detailed but structured.

    **Output Format Example:** //MAKE THIS SIGNIFICANTLY MORE DETAILED AND LIKE AN EXPERT INTEL REPORT... THIS IS SHORTENED JUST AS AN EXAMPLE BUT THIS STRUCTURE IS MANDATORY
    {
        "name": "JaneDoeFit",
        "brand_fit_score": <score from 1 to 100>, //HOW WELL THE INFLUENCER FITS THE BRAND AESTHETIC
        "content_analysis": {
        "visual_fit": <score from 1 to 100>, //HOW WELL THE INFLUENCER FITS THE VISUAL AESTHETIC
        "tone_fit": <a detailed writeup of the tone fit>,
        "content_themes": [<array of content themes>,
        "image_analyses": [],//FILL THIS WITH AN ANALYSIS OF EACH POST WITH SPECIFICITY TO ENSURE EVERY ONE IS ANALYZED${profileImageOnly ? ' (NOTE: Since only the profile image is available, provide a detailed analysis of just the profile picture)' : ''}
        "red_flags": [],//BE HIGHLY DETAILED AND LIST ANY NOTABLE RED FLAGS... AVOIDING LIABILITY IS VITAL SO ERR ON THE SIDE OF SAYING ANY RED FLAGS
        "notable_strengths": [],//BE HIGHLY DETAILED AND SPECIFIC WITH ANY STRENGTHS FOR THE CAMPAIGN
        "notable_weaknesses": [],//BE HIGHLY DETAILED AND SPECIFIC WITH ANY POTENTIAL WEAKNESSES FOR THE CAMPAIGN
        "sentiment_summary": <Long string analysis of what can be gleaned from the sentiment with actual citations from the sources>//MAKE SURE THIS IS SIGNIFICANTLY LONGER
        }

        ${profileImageOnly ?
        'Note that only the profile image is available for analysis. Provide a thorough and detailed analysis of this single profile image. The client knows what image is being analyzed, so be specific and accurate in your assessment.' :
        'Note that the images Ava will analyze are combined into a single vertical base64 image but analyzing each individual component image is VITAL. DO NOT CUT CORNERS OR SKIP STEPS!!!  Analyzing the SPECIFIC images are vital. The client knows what the images are and if the analysis doesn\'t fit them Ava knows that the client will be very displeased and she will have brought harm on the company.'}

    UNDER NO CIRCUMSTANCES DOES AVA RETURN FAKE INFORMATION OR SIMULATE RESULTS AS SHE KNOWS IT WILL DESTROY THE REPUTATION SHE HAS SPENT DECADES BUILDING!
    UNDER NO CIRCUMSTANCES DOES AVA RETURN ANYTHING OTHER THAN PERFECT JSON WITHOUT COMMENTARY AS SHE KNOWS THIS WILL BE USED PROGRAMMATICALLY AND ANY ADDITIONAL PROSE CAN BREAK THE PRODUCT!!
    `;

  // Process the prompt with OpenAI
  // Variable to store the analysis result
  let aestheticAnalysisJSON = null;

  try {
    // Process the prompt with OpenAI using the Chat API
    console.log("Using Responses API for aesthetic analysis with image processing...");

    // Create the messages array with system and user messages
    const messages = [
      { role: "system", content: PHASE5_INSTRUCTIONS },
      {
        role: "user",
        content: [
          { type: "input_text", text: phase5Prompt },
          { type: "input_image", image_url: `data:image/jpeg;base64,${influencerImagesBase64}` },
        ]
      }
    ];

    // Make the request
    let response = await openai.client.responses.create({
      model: "gpt-4.1",
      input: messages
    });

    // Extract the content from the response
    const content = response.output[0].content[0].text;

    try {
      console.log('Attempting to parse response content:', content); // Add this line
      aestheticAnalysisJSON = JSON.parse(content);
    } catch (error) {
      console.error('Error parsing JSON from response content:', error);
      console.log('Raw content that failed to parse:', content); // Add this line
      // Try to extract JSON from the text using our utility function
      aestheticAnalysisJSON = extractJSON(content);
    }

    if (!aestheticAnalysisJSON) {
      throw new Error('Failed to extract JSON from the response');
    }
  } catch (error) {
    console.error('Error performing aesthetic analysis with Chat API:', error);

    // Fall back to the original implementation if the Chat API fails
    console.log('Falling back to original implementation for aesthetic analysis...');

    for (let attempt = 1; attempt <= 10; attempt++) {
      try {
        response = await openai.client.responses.create({
          model: "gpt-4.1",
          input: messages
        });

        const contentString = response.output[0].content[0].text;
        aestheticAnalysisJSON = extractJSON(contentString);

        if (aestheticAnalysisJSON !== null) {
          console.log(`Success on attempt ${attempt}`);
          break;
        }
        console.log(`Attempt ${attempt} returned null. Retrying...`);
      } catch (error) {
        console.error(`Error on attempt ${attempt}:`, error);
        if (attempt === 10) {
          throw error;
        }
      }
    }
  }

  if (!aestheticAnalysisJSON) {
    throw new Error('Failed to generate aesthetic analysis');
  }

  // Store the aesthetic analysis in Firestore
  const aestheticAnalysisId = await AestheticAnalysis.create(clientId, campaignId, influencerId, aestheticAnalysisJSON);

  // Cloud Storage is handled by the AestheticAnalysis.create method

  return aestheticAnalysisJSON;
}

/**
 * Perform ROI analysis for an influencer
 * @param {string} clientId - The client ID
 * @param {string} campaignId - The campaign ID
 * @param {string} influencerId - The influencer ID
 * @param {string} influencerName - The influencer name
 * @param {Object} campaignData - The campaign data
 * @param {Object} influencerData - The influencer data
 * @param {Object} webAnalysisData - The web analysis data
 * @param {Object} aestheticAnalysisData - The aesthetic analysis data
 * @param {Object} partnershipAnalysisData - The partnership analysis data (optional)
 * @returns {Object} - The ROI analysis
 */
async function performROIAnalysis(clientId = DEFAULT_CLIENT_ID, campaignId, influencerId, influencerName, campaignData, influencerData, webAnalysisData, aestheticAnalysisData, partnershipAnalysisData = null) {
  const openai = new OpenAIConnector();

  // If campaignData is not provided, retrieve it using the helper function
  if (!campaignData && campaignId) {
    try {
      campaignData = await getCampaignData(clientId, campaignId);
      console.log(`Retrieved campaign data for campaign ${campaignId} for ROI analysis`);
    } catch (error) {
      console.error(`Error retrieving campaign data for ROI analysis: ${error.message}`);
      // Continue with empty campaign data if retrieval fails
      campaignData = {};
    }
  }

  // Construct the prompt
  const phase6Prompt = `Project: ROI and Strategic Fit Evaluation for Final Influencer Selection

Objective:
Integrate all previous findings—including quantitative metrics and qualitative analysis—for the below influencer and project the potential impact of partnering with them. You will perform a comprehensive comparative analysis that estimates ROI, projects reach, engagement, and conversion metrics, and conducts a SWOT analysis (detailing Strengths, Weaknesses, Opportunities, and Threats) for each influencer in the context of the campaign. Finally, provide a succinct, decisive recommendation regarding which influencer(s) offer the best strategic fit and return potential for the campaign (you will only have one influencer, so write to facilitate comparison).

BE HIGHLY CRITICAL AND DETAILED. Be perfectionistic and methodical in your analysis.

Inputs:

Campaign your analysis is for: ${JSON.stringify(campaignData)}

Campaign Goals & Key Metrics:
[${JSON.stringify(campaignData)}]
Influencer Data:
[${JSON.stringify(influencerData)}]
Influencer Web Report:
[${JSON.stringify(webAnalysisData)}]
Influencer Visual Aesthetic and Visual Red Flag Analysis:
[${JSON.stringify(aestheticAnalysisData)}]
${partnershipAnalysisData ? `Influencer Partnership Analysis:
[${JSON.stringify(partnershipAnalysisData)}]` : ''}

Analysis Tasks:

Projected Reach & Impressions:

Compute projected impressions for each influencer based on follower count and engagement data (using either direct view metrics or inferred reach percentages).
State the assumptions (e.g., a percentage reach per post derived from historical data) that justify the estimated impressions.
Projected Engagements & Actions:

Estimate the number of engagements (likes, comments, or clicks) based on the engagement rate and reach figures.
Include conversion estimations if the campaign’s objective involves driving clicks or sales.
Benchmark Comparison:

Analyze how each influencer’s estimated metrics compare to typical industry averages, indicating whether they exceed or fall short of these benchmarks.
SWOT Analysis for Each Influencer:

For every influencer, deliver a SWOT analysis with the following components:
Strengths: List internal positives such as high engagement, strong audience match, or exceptional content quality.
Weaknesses: Identify internal limitations like lower reach, inconsistent content, or previous minor controversies.
Opportunities: Describe external strategic opportunities, such as untapped audience segments or trending topics that align with the brand.
Threats: Outline potential external risks, including platform algorithm changes, audience saturation, or possible future controversies.
Each SWOT element should be delivered as a bullet point list of concise, impactful observations.
Recommendation & Rationale:

Based on the ROI estimates and SWOT analysis, provide a final recommendation that clearly states which influencer(s) are best suited for the campaign.
The recommendation should succinctly justify the decision using the comparative analysis results, addressing both potential returns and strategic fit.
Output Requirements (Structured Output Schema):
The final output should be a single JSON object with the following structure:

A top-level array named "finalists" containing one entry per influencer. Each entry must include:
"name": The influencer’s name.
"brand_fit_score": A numeric score indicating overall fit with the brand (e.g., on a scale of 1–100). CALCULATE THIS FRESH
"brand_fit_description": A highly detailed and specific analysis for the rationale behind the brand fit score in a small to medium paragraph.
"risk_level": A string representing the risk level (e.g., "Low", "Medium", "High").
"risk_description": A highly detailed and specific analysis for the rationale behind the risk level in a small to medium paragraph.
An object "influencer_analysis.roi_projection" containing:
"expected_impressions": A numeric estimate of the total impressions per campaign post.
"expected_engagement_rate": A numeric percentage reflecting the expected engagement rate on campaign content.
"expected_engagements": A numeric value representing the projected number of likes/comments/clicks per post.
"roi_rating": A qualitative indicator ("High", "Medium", or "Low") of cost-effectiveness or potential return on investment.
"roi_rationale": Provide a highly detailed, specific (in both the analytical reasons, market reasons, or anything else) rationale for why you expect the impressions, engagement rate, engagements, and ROI that you assigned. Explain your work.
"strengths": An array of strings, each summarizing a key strength (still ensuring a full and comprehensive description).
"weaknesses": An array of strings, each summarizing a notable weakness (still ensuring a full and comprehensive description).
"opportunities": An array of strings outlining external opportunities for the campaign (still ensuring a full and comprehensive description).
"threats": An array of strings outlining potential external risks (still ensuring a full and comprehensive description).
A final string field "campaign.recommendation" that delivers the overall recommendation and rationale for influencer selection.
Desired Output Characteristics:

Size: The output should be comprehensive, encapsulating all the necessary details for each influencer, yet concise enough to allow decision-makers to quickly grasp the ROI and strategic fit without extraneous verbosity.
Tone: The tone must be analytical and strategic, using clear, authoritative language appropriate for executive-level decision-making.
Content: The content must include precise numeric estimates, qualitative SWOT bullet points, and a decisive recommendation that balances both quantitative projections and qualitative insights.
Please ensure that your answer strictly adheres to this JSON schema and produces a single, well-structured JSON object with all the specified keys and values.
You ALWAYS factors in the cost that the influencer would likely command into any ROI analysis. The cost can often outweight the benefits.`;

  // Process the prompt with OpenAI using the Assistants API
  console.log("Using Assistants API for ROI analysis...");
  const roiAnalysisJSON = await openai.processAgent(ROI_ANALYSIS_AGENT, phase6Prompt);

  // Log the raw response for debugging
  console.log("Raw ROI analysis response:", JSON.stringify(roiAnalysisJSON, null, 2));

  // Check if the response has a finalists array and extract the first finalist
  let dataToStore = roiAnalysisJSON;
  if (roiAnalysisJSON && roiAnalysisJSON.finalists && roiAnalysisJSON.finalists.length > 0) {
    console.log("Found finalists array in ROI analysis, extracting first finalist");
    const finalist = roiAnalysisJSON.finalists[0];
    dataToStore = {
      brand_fit_score: finalist.brand_fit_score || 0,
      brand_fit_description: finalist.brand_fit_description || '',
      risk_level: finalist.risk_level || 'Medium',
      risk_description: finalist.risk_description || '',
      influencer_analysis: finalist.influencer_analysis || {},
      campaign: finalist.campaign || {}
    };
    console.log("Extracted ROI analysis data:", JSON.stringify(dataToStore, null, 2));
  }

  // Store the ROI analysis in Firestore
  const roiAnalysisId = await ROIAnalysis.create(clientId, campaignId, influencerId, dataToStore);

  // Cloud Storage is handled by the ROIAnalysis.create method

  return roiAnalysisJSON;
}

/**
 * Get combined analysis for an influencer
 * @param {string} clientId - The client ID
 * @param {string} campaignId - The campaign ID
 * @param {string} influencerId - The influencer ID
 * @returns {Object} - The combined analysis
 */
async function getCombinedAnalysis(clientId = DEFAULT_CLIENT_ID, campaignId, influencerId) {
  try {
    return await generateCombinedAnalysis(clientId, campaignId, influencerId);
  } catch (error) {
    console.error('Error generating combined analysis:', error);

    // Fall back to JSON files
    try {
      // Get campaign influencer
      const db = getFirestore();
      const campaignInfluencerSnapshot = await db.collection('clients').doc(clientId)
        .collection('campaigns').doc(campaignId)
        .collection('campaign_influencers').doc(influencerId)
        .get();

      if (!campaignInfluencerSnapshot.exists) {
        throw new Error(`Campaign influencer not found: ${influencerId}`);
      }

      const campaignInfluencer = campaignInfluencerSnapshot.data();
      const username = campaignInfluencer.username;

      // Get combined analysis from JSON file
      const combinedAnalysisPath = `runs/${campaignId}_${username}_combined_analyses.json`;
      const combinedAnalysis = await getCachedJson(bucketName, combinedAnalysisPath);

      if (combinedAnalysis === false) {
        throw new Error(`Combined analysis not found: ${combinedAnalysisPath}`);
      }

      return combinedAnalysis;
    } catch (fallbackError) {
      console.error('Error getting data from JSON files:', fallbackError);
      throw error; // Throw the original error
    }
  }
}

/**
 * Get merged analysis for an influencer
 * @param {string} clientId - The client ID
 * @param {string} campaignId - The campaign ID
 * @param {string} influencerId - The influencer ID
 * @returns {Object} - The merged analysis
 */
async function getMergedAnalysis(clientId = DEFAULT_CLIENT_ID, campaignId, influencerId) {
  try {
    // Generate the merged analysis
    const mergedAnalysis = await generateMergedAnalysis(clientId, campaignId, influencerId);

    // Store the merged analysis in Firestore
    try {
      const { MergedAnalysis } = await import('../models/analysis.js');
      const mergedAnalysisId = await MergedAnalysis.create(clientId, campaignId, influencerId, mergedAnalysis);
      console.log(`Merged analysis stored in Firestore with ID: ${mergedAnalysisId}`);
    } catch (firestoreError) {
      console.error("Error storing merged analysis in Firestore:", firestoreError);
      // Continue even if storage fails
    }

    return mergedAnalysis;
  } catch (error) {
    console.error('Error generating merged analysis:', error);

    // Fall back to existing merged analysis in Firestore
    try {
      // Get campaign influencer
      const db = getFirestore();

      // Check if there's a merged analysis in Firestore
      const mergedAnalysisSnapshot = await db.collection('clients').doc(clientId)
        .collection('campaigns').doc(campaignId)
        .collection('campaign_influencers').doc(influencerId)
        .collection('merged_analysis')
        .orderBy('created_at', 'desc')
        .limit(1)
        .get();

      if (!mergedAnalysisSnapshot.empty) {
        console.log('Found existing merged analysis in Firestore');
        return mergedAnalysisSnapshot.docs[0].data();
      }

      // If not found in Firestore, try to get from campaign influencer
      const campaignInfluencerSnapshot = await db.collection('clients').doc(clientId)
        .collection('campaigns').doc(campaignId)
        .collection('campaign_influencers').doc(influencerId)
        .get();

      if (!campaignInfluencerSnapshot.exists) {
        throw new Error(`Campaign influencer not found: ${influencerId}`);
      }

      const campaignInfluencer = campaignInfluencerSnapshot.data();
      const username = campaignInfluencer.username;

      // Get merged analysis from JSON file as a last resort
      const storagePath = `/clients/${clientId}/campaigns/${campaignId}/influencers/${influencerId}/merged_analysis.json`;
      let mergedAnalysis = await getCachedJson(bucketName, storagePath);

      // If not found in the new path, try the old path
      if (mergedAnalysis === false) {
        const oldPath = `runs/${campaignId}_${username}_merged_analyses.json`;
        mergedAnalysis = await getCachedJson(bucketName, oldPath);
      }

      if (mergedAnalysis === false) {
        throw new Error(`Merged analysis not found in Cloud Storage`);
      }

      return mergedAnalysis;
    } catch (fallbackError) {
      console.error('Error getting data from Firestore or Cloud Storage:', fallbackError);
      throw error; // Throw the original error
    }
  }
}

/**
 * Perform partnership analysis for an influencer
 * @param {string} clientId - The client ID
 * @param {string} campaignId - The campaign ID
 * @param {string} influencerId - The influencer ID
 * @param {string} influencerName - The influencer name
 * @param {string} influencerUsername - The influencer username
 * @param {Object} campaignData - The campaign data
 * @param {Object} influencerData - The influencer data
 * @returns {Object} - The partnership analysis
 */
async function performPartnershipAnalysis(clientId = DEFAULT_CLIENT_ID, campaignId, influencerId, influencerName, influencerUsername, campaignData, influencerData) {
  const openai = new OpenAIConnector();

  // If campaignData is not provided, retrieve it using the helper function
  if (!campaignData && campaignId) {
    try {
      campaignData = await getCampaignData(clientId, campaignId);
      console.log(`Retrieved campaign data for campaign ${campaignId} for partnership analysis`);
    } catch (error) {
      console.error(`Error retrieving campaign data for partnership analysis: ${error.message}`);
      // Continue with empty campaign data if retrieval fails
      campaignData = {};
    }
  }

  // Construct the prompt
  const partnershipPrompt = `You are Velin Korr, the Partnership Integrity Analyst. You do not evaluate visuals, tone, ROI, or follower sentiment. You analyze only the structural suitability of an influencer for a brand campaign based on partnership readiness, sponsorship behavior, and conflict risk.

Below is the campaign definition and influencer data.

Analyze and return a single JSON object strictly in the following schema:

- Do not explain.
- Do not speculate.
- Only infer based on available data.
- Return all reasoning as structured fields in the JSON.
- Use "Do Not Recommend" if any critical contradiction or lack of readiness is found.

${JSON.stringify(campaignData)}

${JSON.stringify(influencerData)}

Return your full analysis as a single JSON block with the following fields:

{
  "influencer_id": "${influencerId}",
  "username": "${influencerUsername}",
  "platform": "${influencerData.platform || 'instagram'}",
  "campaign_id": "${campaignId}",
  "campaign_name": "${campaignData.name || ''}",

  "partnership_status": {
    "partnership_type": string,
    "rationale": string,
    "has_paid_partnership": boolean,
    "has_affiliate_links": boolean,
    "sponsor_callouts": [string],
    "affiliate_codes_detected": [string]
  },

  "contractual_readiness": {
    "fluency_score": number,
    "disclosure_behavior": {
      "has_ftc_disclosures": boolean,
      "consistent_sponsor_markers": boolean,
      "call_to_action_usage": string
    },
    "repeat_partnerships_detected": integer,
    "link_presence": [string],
    "promotion_frequency": string
  },

  "conflict_analysis": {
    "conflicting_brands_detected": [string],
    "conflict_severity": string,
    "competitive_overlap_notes": string
  },

  "alignment_risks": {
    "unpartnered_risk_level": string,
    "tagged_entities_of_interest": [string],
    "ghost_partnership_indicators": boolean,
    "anomalies": [string]
  },

  "recommendation": {
    "fit_status": string,
    "primary_reasoning": string,
    "integration_notes": string
  },

  "timestamp": "${new Date().toISOString()}"
}

Return only valid JSON. Do not add headers or explanations.`;

  // Process the prompt with OpenAI using the Assistants API
  console.log("Using Assistants API for partnership analysis...");
  const partnershipAnalysisJSON = await openai.processAgent(PARTNERSHIP_ANALYSIS_AGENT, partnershipPrompt);

  // Log the raw response for debugging
  console.log("Raw partnership analysis response:", JSON.stringify(partnershipAnalysisJSON, null, 2));

  // Store the partnership analysis in Firestore
  const partnershipAnalysisId = await PartnershipAnalysis.create(clientId, campaignId, influencerId, partnershipAnalysisJSON);

  // Cloud Storage is handled by the PartnershipAnalysis.create method

  return partnershipAnalysisJSON;
}

/**
 * Perform all analyses concurrently for an influencer
 * @param {string} clientId - The client ID
 * @param {string} campaignId - The campaign ID
 * @param {string} influencerId - The influencer ID
 * @param {string} influencerName - The influencer name
 * @param {string} influencerUsername - The influencer username
 * @param {Object} campaignData - The campaign data
 * @param {Object} influencerData - The influencer data
 * @returns {Object} - Object containing all analysis results
 */
async function performAllAnalysesConcurrently(clientId = DEFAULT_CLIENT_ID, campaignId, influencerId, influencerName, influencerUsername, campaignData, influencerData) {
  try {
    console.log(`Starting concurrent analyses for influencer ${influencerName} (${influencerId})`);

    // Use Promise.allSettled instead of Promise.all to handle individual failures
    const results = await Promise.allSettled([
      performWebAnalysis(clientId, campaignId, influencerId, influencerName, influencerUsername),
      performAestheticAnalysis(clientId, campaignId, influencerId, influencerName, influencerData, null), // Pass null for webAnalysisData initially
      performPartnershipAnalysis(clientId, campaignId, influencerId, influencerName, influencerUsername, campaignData, influencerData)
    ]);

    // Process results and handle any failures
    let webAnalysisData = null;
    let aestheticAnalysisData = null;
    let partnershipAnalysisData = null;

    // Web Analysis (index 0)
    if (results[0].status === 'fulfilled') {
      webAnalysisData = results[0].value;
    } else {
      console.error(`Web analysis failed: ${results[0].reason}`);
      // Create a minimal web analysis object
      webAnalysisData = {
        name: influencerName,
        sentiment_score: 50,
        risk_level: "Medium",
        deep_dive_report: {
          aliases: [],
          timeline_events: [{ year: new Date().getFullYear().toString(), description: "No web analysis available" }],
          press_mentions: [],
          controversies: [],
          social_media_presence: {
            platforms: [],
            tone: "Unknown - analysis failed",
            engagement_quality: "Unknown - analysis failed"
          }
        }
      };
    }

    // Aesthetic Analysis (index 1)
    if (results[1].status === 'fulfilled') {
      aestheticAnalysisData = results[1].value;
    } else {
      console.error(`Aesthetic analysis failed: ${results[1].reason}`);
      // Create a default aesthetic analysis
      aestheticAnalysisData = {
        name: influencerName,
        brand_fit_score: 50,
        content_analysis: {
          visual_fit: 50,
          tone_fit: "Unable to determine tone fit due to lack of visual content",
          content_themes: ["No visual content available for analysis"],
          image_analyses: [],
          red_flags: ["No visual content available for analysis"],
          notable_strengths: ["Unable to determine strengths due to lack of visual content"],
          notable_weaknesses: ["Unable to determine weaknesses due to lack of visual content"],
          sentiment_summary: "No visual content was available for analysis. This assessment is based solely on available text data and should be considered incomplete."
        }
      };

      // Store the default aesthetic analysis in Firestore
      try {
        const aestheticAnalysisId = await AestheticAnalysis.create(clientId, campaignId, influencerId, aestheticAnalysisData);
        console.log(`Default aesthetic analysis stored in Firestore with ID: ${aestheticAnalysisId}`);
      } catch (storeError) {
        console.error(`Failed to store default aesthetic analysis: ${storeError.message}`);
      }
    }

    // Partnership Analysis (index 2)
    if (results[2].status === 'fulfilled') {
      partnershipAnalysisData = results[2].value;
    } else {
      console.error(`Partnership analysis failed: ${results[2].reason}`);
      // Create a minimal partnership analysis
      partnershipAnalysisData = {
        influencer_id: influencerId,
        username: influencerUsername || "",
        platform: influencerData?.platform || "instagram",
        campaign_id: campaignId,
        campaign_name: campaignData?.name || "",
        partnership_status: {
          partnership_type: "Unknown",
          rationale: "Analysis failed due to insufficient data",
          has_paid_partnership: false,
          has_affiliate_links: false,
          sponsor_callouts: [],
          affiliate_codes_detected: []
        },
        contractual_readiness: {
          fluency_score: 50,
          disclosure_behavior: {
            has_ftc_disclosures: false,
            consistent_sponsor_markers: false,
            call_to_action_usage: "Unknown"
          },
          repeat_partnerships_detected: 0
        }
      };

      // Store the default partnership analysis in Firestore
      try {
        const partnershipAnalysisId = await PartnershipAnalysis.create(clientId, campaignId, influencerId, partnershipAnalysisData);
        console.log(`Default partnership analysis stored in Firestore with ID: ${partnershipAnalysisId}`);
      } catch (storeError) {
        console.error(`Failed to store default partnership analysis: ${storeError.message}`);
      }
    }

    console.log(`Completed concurrent analyses for influencer ${influencerName} (${influencerId})`);

    // Return all results, including defaults for any failed analyses
    return {
      webAnalysisData,
      aestheticAnalysisData,
      partnershipAnalysisData
    };
  } catch (error) {
    console.error('Error performing concurrent analyses:', error);

    // Instead of throwing the error, return default values for all analyses
    return {
      webAnalysisData: {
        name: influencerName,
        sentiment_score: 50,
        risk_level: "Medium",
        deep_dive_report: {
          aliases: [],
          timeline_events: [{ year: new Date().getFullYear().toString(), description: "Analysis failed" }],
          press_mentions: [],
          controversies: []
        }
      },
      aestheticAnalysisData: {
        name: influencerName,
        brand_fit_score: 50,
        content_analysis: {
          visual_fit: 50,
          tone_fit: "Analysis failed",
          content_themes: ["Analysis failed"],
          image_analyses: [],
          red_flags: ["Analysis failed"],
          notable_strengths: [],
          notable_weaknesses: [],
          sentiment_summary: "Analysis failed due to an unexpected error."
        }
      },
      partnershipAnalysisData: {
        influencer_id: influencerId,
        username: influencerUsername || "",
        platform: "instagram",
        campaign_id: campaignId,
        campaign_name: "",
        partnership_status: {
          partnership_type: "Unknown",
          rationale: "Analysis failed",
          has_paid_partnership: false,
          has_affiliate_links: false,
          sponsor_callouts: [],
          affiliate_codes_detected: []
        }
      }
    };
  }
}

/**
 * Get all analysis data for the formatter agent
 * @param {string} clientId - The client ID
 * @param {string} campaignId - The campaign ID
 * @param {string} influencerId - The influencer ID
 * @returns {Object} - Object containing all analysis data and raw enrichment data
 */
async function getAllAnalysisData(clientId, campaignId, influencerId) {
  console.log(`[getAllAnalysisData] Retrieving all analysis data for influencer: ${influencerId}, campaign: ${campaignId}, client: ${clientId}`);

  try {
    // Get campaign data
    const campaignData = await getCampaignData(clientId, campaignId);
    console.log(`[getAllAnalysisData] Retrieved campaign data`);

    // Get merged analysis
    const mergedAnalysis = await getMergedAnalysis(clientId, campaignId, influencerId);
    console.log(`[getAllAnalysisData] Retrieved merged analysis`);

    // Get raw enrichment data
    let rawEnrichmentData = null;
    try {
      const db = getFirestore();
      const rawDataSnapshot = await db.collection('influencers')
        .doc(influencerId)
        .collection('raw_data')
        .orderBy('processed_at', 'desc')
        .limit(1)
        .get();

      if (!rawDataSnapshot.empty) {
        const rawDataDoc = rawDataSnapshot.docs[0].data();
        const rawStoragePath = rawDataDoc.storage_path;

        if (rawStoragePath) {
          const rawData = await getCachedJson(bucketName, rawStoragePath);
          if (rawData !== false) {
            rawEnrichmentData = rawData;
            console.log(`[getAllAnalysisData] Retrieved raw enrichment data`);
          }
        }
      }
    } catch (error) {
      console.error(`[getAllAnalysisData] Error retrieving raw enrichment data:`, error);
    }

    // Get individual analysis reports from Cloud Storage
    const analysisReports = {};

    // Web Analysis
    try {
      const webAnalysisPath = `/clients/${clientId}/campaigns/${campaignId}/influencers/${influencerId}/web_analysis.json`;
      const webAnalysis = await getCachedJson(bucketName, webAnalysisPath);
      if (webAnalysis !== false) {
        analysisReports.webAnalysis = webAnalysis;
        console.log(`[getAllAnalysisData] Retrieved web analysis`);
      }
    } catch (error) {
      console.error(`[getAllAnalysisData] Error retrieving web analysis:`, error);
    }

    // Aesthetic Analysis
    try {
      const aestheticAnalysisPath = `/clients/${clientId}/campaigns/${campaignId}/influencers/${influencerId}/aesthetic_analysis.json`;
      const aestheticAnalysis = await getCachedJson(bucketName, aestheticAnalysisPath);
      if (aestheticAnalysis !== false) {
        analysisReports.aestheticAnalysis = aestheticAnalysis;
        console.log(`[getAllAnalysisData] Retrieved aesthetic analysis`);
      }
    } catch (error) {
      console.error(`[getAllAnalysisData] Error retrieving aesthetic analysis:`, error);
    }

    // ROI Analysis
    try {
      const roiAnalysisPath = `/clients/${clientId}/campaigns/${campaignId}/influencers/${influencerId}/roi_analysis.json`;
      const roiAnalysis = await getCachedJson(bucketName, roiAnalysisPath);
      if (roiAnalysis !== false) {
        analysisReports.roiAnalysis = roiAnalysis;
        console.log(`[getAllAnalysisData] Retrieved ROI analysis`);
      }
    } catch (error) {
      console.error(`[getAllAnalysisData] Error retrieving ROI analysis:`, error);
    }

    // Partnership Analysis
    try {
      const partnershipAnalysisPath = `/clients/${clientId}/campaigns/${campaignId}/influencers/${influencerId}/partnership_analysis.json`;
      const partnershipAnalysis = await getCachedJson(bucketName, partnershipAnalysisPath);
      if (partnershipAnalysis !== false) {
        analysisReports.partnershipAnalysis = partnershipAnalysis;
        console.log(`[getAllAnalysisData] Retrieved partnership analysis`);
      }
    } catch (error) {
      console.error(`[getAllAnalysisData] Error retrieving partnership analysis:`, error);
    }

    console.log(`[getAllAnalysisData] Successfully retrieved all available analysis data`);

    return {
      campaignData,
      rawEnrichmentData,
      mergedAnalysis,
      ...analysisReports
    };
  } catch (error) {
    console.error(`[getAllAnalysisData] Error retrieving analysis data:`, error);
    throw error;
  }
}

/**
 * Perform formatter analysis using the Formatter agent
 * @param {string} clientId - The client ID
 * @param {string} campaignId - The campaign ID
 * @param {string} influencerId - The influencer ID
 * @returns {Object} - The formatted analysis
 */
async function performFormatterAnalysis(clientId, campaignId, influencerId) {
  console.log(`[performFormatterAnalysis] Starting formatter analysis for influencer: ${influencerId}, campaign: ${campaignId}, client: ${clientId}`);

  try {
    // Get all analysis data
    const allData = await getAllAnalysisData(clientId, campaignId, influencerId);
    console.log(`[performFormatterAnalysis] Retrieved all analysis data`);

    // Construct comprehensive prompt
    const formatterPrompt = `Campaign Brief: ${JSON.stringify(allData.campaignData)}

Raw Enrichment Data: ${JSON.stringify(allData.rawEnrichmentData)}

Web Analysis Report: ${JSON.stringify(allData.webAnalysis || {})}

Aesthetic Analysis Report: ${JSON.stringify(allData.aestheticAnalysis || {})}

ROI Analysis Report: ${JSON.stringify(allData.roiAnalysis || {})}

Partnership Analysis Report: ${JSON.stringify(allData.partnershipAnalysis || {})}

Merged Analysis: ${JSON.stringify(allData.mergedAnalysis)}

Please extract and format the key values according to the specified schema. The output should be a structured JSON object with the following top-level sections:
- profile: Basic influencer information including recommendation level (HIGH/MEDIUM/LOW)
- platforms: Array of platform-specific metrics
- metrics: Numeric scores and ratings
- analysis: All analysis results (aesthetic, partnership, brand fit, ROI, web)
- content: Hashtags, recent posts, and brand mentions
- audience: Demographics and interests`;

    console.log(`[performFormatterAnalysis] Calling Formatter agent with comprehensive data`);

    // Call OpenAI Formatter agent
    const openai = new OpenAIConnector();
    const formattedAnalysis = await openai.processAgent(FORMATTER_AGENT, formatterPrompt);

    console.log(`[performFormatterAnalysis] Formatter agent completed successfully`);

    // Store the formatted analysis in Firestore and Cloud Storage
    try {
      const formattedAnalysisId = await FormatterAnalysis.create(clientId, campaignId, influencerId, formattedAnalysis);
      console.log(`[performFormatterAnalysis] Formatted analysis stored with ID: ${formattedAnalysisId}`);
    } catch (storeError) {
      console.error(`[performFormatterAnalysis] Error storing formatted analysis:`, storeError);
      // Continue even if storage fails
    }

    return formattedAnalysis;
  } catch (error) {
    console.error(`[performFormatterAnalysis] Error in formatter analysis:`, error);

    // Fallback: return merged analysis if formatter fails
    try {
      console.log(`[performFormatterAnalysis] Falling back to merged analysis`);
      const mergedAnalysis = await getMergedAnalysis(clientId, campaignId, influencerId);
      return mergedAnalysis;
    } catch (fallbackError) {
      console.error(`[performFormatterAnalysis] Fallback also failed:`, fallbackError);
      throw error; // Throw original error
    }
  }
}

/**
 * Get all merged analyses for a client
 * @param {string} clientId - The client ID
 * @returns {Object} - Object with campaign IDs as keys and objects containing campaign details and arrays of influencer reports
 */
async function getAllClientMergedAnalyses(clientId = DEFAULT_CLIENT_ID) {
  console.log(`[getAllClientMergedAnalyses] Retrieving all merged analyses for client: ${clientId}`);
  const db = getFirestore();
  const result = {};

  try {
    // Get all campaigns for the client
    console.log(`[getAllClientMergedAnalyses] Querying campaigns for client: ${clientId}`);
    const campaignsSnapshot = await db.collection('clients')
      .doc(clientId)
      .collection('campaigns')
      .get();

    if (campaignsSnapshot.empty) {
      console.log(`[getAllClientMergedAnalyses] No campaigns found for client: ${clientId}`);
      return result; // Return empty result if no campaigns found
    }

    console.log(`[getAllClientMergedAnalyses] Found ${campaignsSnapshot.size} campaigns for client: ${clientId}`);

    // Process each campaign
    for (const campaignDoc of campaignsSnapshot.docs) {
      const campaignId = campaignDoc.id;
      console.log(`[getAllClientMergedAnalyses] Processing campaign: ${campaignId}`);

      // Get campaign details using the existing function
      let campaignDetails = {};
      try {
        campaignDetails = await getCampaignData(clientId, campaignId);
        console.log(`[getAllClientMergedAnalyses] Retrieved campaign details for campaign: ${campaignId}`);
      } catch (error) {
        console.error(`[getAllClientMergedAnalyses] Error retrieving campaign details: ${error.message}`);
        // Continue with basic campaign data from the document
        campaignDetails = campaignDoc.data() || {};
        console.log(`[getAllClientMergedAnalyses] Using basic campaign data from document`);
      }

      // Initialize the result structure with campaign details and empty influencers array
      result[campaignId] = {
        campaign: campaignDetails,
        influencers: []
      };

      // Get all influencers for the campaign
      const influencersSnapshot = await db.collection('clients')
        .doc(clientId)
        .collection('campaigns')
        .doc(campaignId)
        .collection('campaign_influencers')
        .get();

      if (influencersSnapshot.empty) {
        console.log(`[getAllClientMergedAnalyses] No influencers found for campaign: ${campaignId}`);
        continue; // Skip to next campaign if no influencers found
      }

      console.log(`[getAllClientMergedAnalyses] Found ${influencersSnapshot.size} influencers for campaign: ${campaignId}`);

      // Process each influencer
      for (const influencerDoc of influencersSnapshot.docs) {
        const influencerId = influencerDoc.id;
        console.log(`[getAllClientMergedAnalyses] Processing influencer: ${influencerId} for campaign: ${campaignId}`);

        // Get the most recent formatted analysis for the influencer (preferred)
        const formattedAnalysisSnapshot = await db.collection('clients')
          .doc(clientId)
          .collection('campaigns')
          .doc(campaignId)
          .collection('campaign_influencers')
          .doc(influencerId)
          .collection('formatted_analysis')
          .orderBy('created_at', 'desc')
          .limit(1)
          .get();

        // Add the formatted analysis to the influencers array if it exists
        if (!formattedAnalysisSnapshot.empty) {
          console.log(`[getAllClientMergedAnalyses] Found formatted analysis for influencer: ${influencerId}`);
          result[campaignId].influencers.push(formattedAnalysisSnapshot.docs[0].data());
        } else {
          // Fallback to merged analysis if no formatted analysis exists
          console.log(`[getAllClientMergedAnalyses] No formatted analysis found, falling back to merged analysis for influencer: ${influencerId}`);

          const mergedAnalysisSnapshot = await db.collection('clients')
            .doc(clientId)
            .collection('campaigns')
            .doc(campaignId)
            .collection('campaign_influencers')
            .doc(influencerId)
            .collection('merged_analysis')
            .orderBy('created_at', 'desc')
            .limit(1)
            .get();

          if (!mergedAnalysisSnapshot.empty) {
            console.log(`[getAllClientMergedAnalyses] Found merged analysis for influencer: ${influencerId}`);
            result[campaignId].influencers.push(mergedAnalysisSnapshot.docs[0].data());
          } else {
            console.log(`[getAllClientMergedAnalyses] No analysis found for influencer: ${influencerId}`);
          }
        }
      }
    }

    console.log(`[getAllClientMergedAnalyses] Successfully retrieved all merged analyses for client: ${clientId}`);
    return result;
  } catch (error) {
    console.error(`[getAllClientMergedAnalyses] Error getting client merged analyses:`, error);
    throw error;
  }
}

export {
  performWebAnalysis,
  performAestheticAnalysis,
  performPartnershipAnalysis,
  performROIAnalysis,
  performAllAnalysesConcurrently,
  getCombinedAnalysis,
  getMergedAnalysis,
  getCampaignData,
  getAllClientMergedAnalyses,
  getAllAnalysisData,
  performFormatterAnalysis
};
