// influencer-routes.js
// Influencer-related routes

import express from 'express';
import { enrichInfluencer, getInfluencer, addInfluencerToCampaign, updateInfluencerStatus } from '../services/influencer-service.js';
import {
  performAllAnalysesConcurrently,
  performROIAnalysis,
  getMergedAnalysis,
  getCampaignData,
  performFormatterAnalysis
} from '../services/analysis-service.js';
import { DEFAULT_CLIENT_ID } from '../config/constants.js';

const router = express.Router();

/**
 * Enrich an influencer
 * POST /api/influencers/enrich
 */
router.post('/enrich', async (req, res) => {
  try {
    const { username, platform = 'instagram' } = req.body;
    const result = await enrichInfluencer(username, platform);
    res.status(200).json(result);
  } catch (error) {
    console.error('Error enriching influencer:', error);
    res.status(500).json({ error: 'Failed to enrich influencer' });
  }
});

/**
 * Get an influencer
 * GET /api/influencers/:influencerId
 */
router.get('/:influencerId', async (req, res) => {
  try {
    const { influencerId } = req.params;
    const influencer = await getInfluencer(influencerId);
    res.status(200).json(influencer);
  } catch (error) {
    console.error('Error getting influencer:', error);
    res.status(500).json({ error: 'Failed to get influencer' });
  }
});

/**
 * Add an influencer to a campaign
 * POST /api/influencers/campaigns/:campaignId
 */
router.post('/campaigns/:campaignId', async (req, res) => {
  try {
    const { campaignId } = req.params;
    const { influencerId, username, clientId = DEFAULT_CLIENT_ID } = req.body;
    await addInfluencerToCampaign(clientId, campaignId, influencerId, username);
    res.status(200).json({ success: true });
  } catch (error) {
    console.error('Error adding influencer to campaign:', error);
    res.status(500).json({ error: 'Failed to add influencer to campaign' });
  }
});

/**
 * Update an influencer's status in a campaign
 * PUT /api/influencers/campaigns/:campaignId/:influencerId/status
 */
router.put('/campaigns/:campaignId/:influencerId/status', async (req, res) => {
  try {
    const { campaignId, influencerId } = req.params;
    const { status, clientId = DEFAULT_CLIENT_ID } = req.body;
    const result = await updateInfluencerStatus(clientId, campaignId, influencerId, status);
    res.status(200).json(result);
  } catch (error) {
    console.error('Error updating influencer status:', error);
    res.status(500).json({ error: 'Failed to update influencer status' });
  }
});

/**
 * Complete influencer analysis - Enrich and analyze an influencer in one step
 * POST /api/influencers/complete-analysis
 */
router.post('/complete-analysis', async (req, res) => {
  try {
    // Step 1: Validate input parameters
    const { username, campaignId, clientId, platform, forceRefresh = false } = req.body;

    // Validate required parameters
    if (!username) {
      return res.status(400).json({ error: 'Username is required' });
    }

    if (!campaignId) {
      return res.status(400).json({ error: 'Campaign ID is required' });
    }

    if (!clientId) {
      return res.status(400).json({ error: 'Client ID is required' });
    }

    if (!platform) {
      return res.status(400).json({ error: 'Platform is required' });
    }

    console.log(`Starting complete analysis for influencer ${username} on ${platform} for campaign ${campaignId} and client ${clientId}`);

    // Step 2: Retrieve campaign data
    console.log(`Retrieving campaign data for campaign ${campaignId}`);
    const campaignData = await getCampaignData(clientId, campaignId);

    // Step 3: Enrich influencer data
    console.log(`Enriching influencer data for ${username} on ${platform}`);
    const { influencerId, processedData } = await enrichInfluencer(username, platform, forceRefresh);
    const influencerName = processedData.name || username;

    // Step 4: Add influencer to campaign if not already added
    console.log(`Adding influencer ${influencerId} to campaign ${campaignId}`);
    await addInfluencerToCampaign(clientId, campaignId, influencerId, username);

    // Step 5: Perform all analyses concurrently
    console.log(`Performing concurrent analyses for influencer ${influencerName}`);
    const { webAnalysisData, aestheticAnalysisData, partnershipAnalysisData } =
      await performAllAnalysesConcurrently(
        clientId,
        campaignId,
        influencerId,
        influencerName,
        username,
        campaignData,
        processedData
      );

    // Step 6: Perform ROI analysis
    console.log(`Performing ROI analysis for influencer ${influencerName}`);
    await performROIAnalysis(
      clientId,
      campaignId,
      influencerId,
      influencerName,
      campaignData,
      processedData,
      webAnalysisData,
      aestheticAnalysisData,
      partnershipAnalysisData
    );

    // Step 7: Get merged analysis
    console.log(`Getting merged analysis for influencer ${influencerName}`);
    const mergedAnalysis = await getMergedAnalysis(clientId, campaignId, influencerId);

    // Step 8: Perform formatter analysis
    console.log(`Performing formatter analysis for influencer ${influencerName}`);
    const formattedAnalysis = await performFormatterAnalysis(clientId, campaignId, influencerId);

    // Step 9: Return complete result
    console.log(`Complete analysis finished for influencer ${username}`);
    res.status(200).json(formattedAnalysis);
  } catch (error) {
    console.error('Error performing complete influencer analysis:', error);

    // Create a minimal response with default values instead of returning an error
    // This ensures the client always gets a valid response even if some parts of the analysis failed
    const defaultResponse = {
      profileInfo: {
        username: username,
        name: username,
        bio: "Analysis incomplete",
        profilePicture: "",
        platform: platform,
        followers: 0,
        engagement: "0%",
        metrics: {
          brandAesthetic: "0/100",
          styleAnalysis: "0.0",
          vibeAlignment: "0.0",
          visualAlignment: "0.0"
        }
      },
      webAnalysis: {
        sentiment: "neutral",
        riskLevel: "Medium",
        aliases: [],
        timelineEvents: [],
        pressMentions: [],
        controversies: []
      },
      aestheticAnalysis: {
        keyObservations: ["Analysis incomplete"],
        verdict: "Analysis incomplete",
        verdictDescription: "The analysis could not be completed due to technical issues."
      },
      roiAnalysis: {
        projectedReach: 0,
        engagementRate: "0%",
        costEffectiveness: "Unknown",
        strengths: ["Analysis incomplete"],
        weaknesses: ["Analysis incomplete"],
        opportunities: ["Analysis incomplete"],
        threats: ["Analysis incomplete"]
      },
      partnershipAnalysis: {
        partnershipType: "Unknown",
        hasPaidPartnerships: false,
        hasAffiliateLinks: false,
        sponsorCallouts: [],
        affiliateCodesDetected: []
      },
      overallRecommendation: "Analysis incomplete. Please try again later."
    };

    // Log that we're returning a default response
    console.log('Returning default response due to analysis failure');

    // Return a 200 status with the default response instead of an error
    res.status(200).json(defaultResponse);
  }
});

export default router;
