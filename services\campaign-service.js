// campaign-service.js
// Campaign-related business logic

import { getFirestore, Timestamp } from 'firebase-admin/firestore';
import Campaign from '../models/campaign.js';
import OpenAIConnector from '../connectors/openai-connector.js';
import { writeJsonToBucket } from '../helpers/storage-helpers.js';
import { CAMPAIGN_ANALYSIS_AGENT } from '../config/constants.js';
import { DEFAULT_CLIENT_ID } from '../config/constants.js';

/**
 * Create a campaign
 * @param {string} clientId - The client ID
 * @param {Object} campaignData - The campaign data
 * @returns {string} - The campaign ID
 */
async function createCampaign(clientId = DEFAULT_CLIENT_ID, campaignData) {
  return await Campaign.create(clientId, campaignData);
}

/**
 * Get a campaign
 * @param {string} clientId - The client ID
 * @param {string} campaignId - The campaign ID
 * @returns {Campaign} - The campaign
 */
async function getCampaign(clientId = DEFAULT_CLIENT_ID, campaignId) {
  return await Campaign.findById(clientId, campaignId);
}

/**
 * Update a campaign
 * @param {string} clientId - The client ID
 * @param {string} campaignId - The campaign ID
 * @param {Object} campaignData - The campaign data
 * @returns {Campaign} - The updated campaign
 */
async function updateCampaign(clientId = DEFAULT_CLIENT_ID, campaignId, campaignData) {
  return await Campaign.update(clientId, campaignId, campaignData);
}

/**
 * Generate a campaign brief using OpenAI
 * @param {Object} input - The input data
 * @returns {Object} - The campaign brief
 */
async function generateCampaignBrief(input) {
  const openai = new OpenAIConnector();

  // Construct the prompt
  const phase1Prompt = `Project: Develop a Comprehensive Campaign Brief for **${input.campaign.name}**

**Overview:** We are initiating an influencer marketing campaign. Below are the campaign inputs and requirements. Your task is to synthesize this information into a structured campaign brief that will guide all subsequent phases.

**Campaign Inputs:**
${JSON.stringify(input.campaign)}

**Tasks:**
Craft the ideal influencer archetype for the campaign ensuring the influencers are of the optimal size and focus.

Additionally, please provide:
1. A detailed influencer description (2-3 sentences) that can be used for AI search to find matching influencers
2. The main platform to search for influencers (instagram, youtube, tiktok, etc.)`;

  // Process the prompt with OpenAI
  const campaignJSON = await openai.processAgent(CAMPAIGN_ANALYSIS_AGENT, phase1Prompt);

  // Store the campaign brief in Firestore
  const db = getFirestore();
  const clientId = input.client_id || DEFAULT_CLIENT_ID;
  const campaignRef = db.collection('clients').doc(clientId).collection('campaigns').doc();
  const campaignId = campaignRef.id;

  // Prepare campaign data
  const campaignData = {
    name: campaignJSON.name,
    report_id: campaignId,
    product_description: campaignJSON.product_description,
    influencer_gender: campaignJSON.influencer_gender || '',
    influencer_niche: campaignJSON.influencer_niche || '',
    influencer_age: campaignJSON.influencer_age || '',
    influencer_personality: campaignJSON.influencer_personality || '',
    influencer_aesthetic: campaignJSON.influencer_aesthetic || '',
    min_follower_count: campaignJSON.min_follower_count || 0,
    max_follower_count: campaignJSON.max_follower_count || 0,
    min_engagement_rate: campaignJSON.min_engagement_rate || 0,
    influencer_description: campaignJSON.influencer_description || '',
    main_platform: campaignJSON.main_platform || 'instagram',
    created_at: Timestamp.now(),
    updated_at: Timestamp.now(),
    status: 'active'
  };

  // Save to Firestore
  await campaignRef.set(campaignData);

  // Save to Cloud Storage for backward compatibility
  await writeJsonToBucket(`/clients/${clientId}/campaigns/${campaignId}/campaign_analysis.json`, campaignJSON);
  await writeJsonToBucket(`/runs/${campaignId}_campaign_analysis.json`, campaignJSON);

  return {
    campaignId,
    campaignData: campaignJSON
  };
}

export {
  createCampaign,
  getCampaign,
  updateCampaign,
  generateCampaignBrief
};
